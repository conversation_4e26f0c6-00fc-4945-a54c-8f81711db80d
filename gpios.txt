    for the i2c :
SCL on GPIO10
and SDA on GPIO11
    for the spi:
MISO on GPIO20
MOSI on GPIO18
SPI_CLK on GPIO19
SPI_CS on GPIO21

    for the sensors :
the sd card detect on GPIO 15
batterie indicator using a voltage devider circuit on gpio2
RGB Adafruit LED on GPIO8
Side Button on GPIO7
the MPU6050 interupt pin on GPIO6
the first ir position sensor on GPIO1 (using ADC) (QRE1113P)
the second ir position sensor on GPIO0 (using ADC) (QRE1113P)

    Note
the Oled 0.96" display is using the i2c (0x3c)
the SDP33 sensor is using the i2c (0x21)
the SHT45 sensor is using the i2c (0x44)
the MPU6050 sensor is using the i2c (0x68)
the MSD card is using the spi